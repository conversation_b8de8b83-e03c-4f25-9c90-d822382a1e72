"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, ChevronDown } from "lucide-react"
import {
  Calendar,
  Settings,
  Bus,
  UserCheck,
  BookOpen,
  HelpCircle,
  GraduationCap,
  FileText,
  UserX,
  DollarSign,
  CreditCard,
  TrendingUp,
  Wallet,
  Bell,
  KeyRound,
  HomeIcon,
  UserMinus,
  Camera,
  ClipboardCheck,
  PenTool,
  Users,
} from "lucide-react"

const erpModules = [
  { name: "Dashboard", icon: <Settings className="w-4 h-4" />, href: "/services#dashboard" },
  { name: "Annual Calendar", icon: <Calendar className="w-4 h-4" />, href: "/services#calendar" },
  { name: "Academic Setup", icon: <BookOpen className="w-4 h-4" />, href: "/services#academic" },
  { name: "Transport Fee", icon: <Bus className="w-4 h-4" />, href: "/services#transport" },
  { name: "Users/Staff", icon: <Users className="w-4 h-4" />, href: "/services#staff" },
  { name: "Leave Management", icon: <UserX className="w-4 h-4" />, href: "/services#leave" },
  { name: "Timetable Overview", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#timetable" },
  { name: "Proxy Request", icon: <UserCheck className="w-4 h-4" />, href: "/services#proxy" },
  { name: "Enquiry Management", icon: <HelpCircle className="w-4 h-4" />, href: "/services#enquiry" },
  { name: "Student Management", icon: <GraduationCap className="w-4 h-4" />, href: "/services#student" },
  { name: "Documents", icon: <FileText className="w-4 h-4" />, href: "/services#documents" },
  { name: "Student Attendance", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#attendance" },
  { name: "Salary", icon: <DollarSign className="w-4 h-4" />, href: "/services#salary" },
  { name: "Fee Collection", icon: <CreditCard className="w-4 h-4" />, href: "/services#fees" },
  { name: "Expense Management", icon: <TrendingUp className="w-4 h-4" />, href: "/services#expense" },
  { name: "Passbook", icon: <Wallet className="w-4 h-4" />, href: "/services#passbook" },
  { name: "Circulars", icon: <Bell className="w-4 h-4" />, href: "/services#circulars" },
  { name: "Exam", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#exam" },
  { name: "Gate Pass", icon: <KeyRound className="w-4 h-4" />, href: "/services#gatepass" },
  { name: "Classwork", icon: <PenTool className="w-4 h-4" />, href: "/services#classwork" },
  { name: "Homework", icon: <HomeIcon className="w-4 h-4" />, href: "/services#homework" },
  { name: "Student Leave", icon: <UserMinus className="w-4 h-4" />, href: "/services#studentleave" },
  { name: "Face Attendance", icon: <Camera className="w-4 h-4" />, href: "/services#faceattendance" },
]

const navigation = [
  { name: "Home", href: "/" },
  { name: "Service", href: "/services", hasDropdown: true },
  { name: "Support", href: "/support" },
  { name: "About US", href: "/about" },
  { name: "Talk to Us", href: "/contact" },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)
  const pathname = usePathname()

  return (
    <header className="bg-black text-white sticky top-0 z-50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5">
            <motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-2xl font-bold text-white">
              Rann Dass
            </motion.span>
          </Link>
        </div>

        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        <div className="hidden lg:flex lg:gap-x-12">
          {navigation.map((item) => (
            <div key={item.name} className="relative">
              {item.hasDropdown ? (
                <div
                  className="relative"
                  onMouseEnter={() => setServicesDropdownOpen(true)}
                  onMouseLeave={() => setServicesDropdownOpen(false)}
                >
                  <Link
                    href={item.href}
                    className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] flex items-center gap-1 ${
                      pathname === item.href ? "text-[#FD904B]" : "text-white"
                    }`}
                  >
                    {item.name}
                    <motion.div
                      animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.div>
                  </Link>

                  <AnimatePresence>
                    {servicesDropdownOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden"
                      >
                        <div className="p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                            Comprehensive ERP Modules
                          </h3>
                          <div className="grid grid-cols-1 gap-2 max-h-96 overflow-y-auto">
                            {erpModules.map((module, index) => (
                              <motion.div
                                key={module.name}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.02 }}
                              >
                                <Link
                                  href={module.href}
                                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                                  onClick={() => setServicesDropdownOpen(false)}
                                >
                                  <div className="text-[#FD904B] group-hover:scale-110 transition-transform">
                                    {module.icon}
                                  </div>
                                  <span className="text-sm text-gray-700 group-hover:text-[#FD904B] transition-colors">
                                    {module.name}
                                  </span>
                                </Link>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] ${
                    pathname === item.href ? "text-[#FD904B]" : "text-white"
                  }`}
                >
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </div>
      </nav>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="lg:hidden">
          <div className="fixed inset-0 z-50" />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <Link href="/" className="-m-1.5 p-1.5">
                <span className="text-xl font-bold text-white">Rann Dass</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-white"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <div key={item.name}>
                      {item.hasDropdown ? (
                        <div>
                          <button
                            className={`-mx-3 flex w-full items-center justify-between rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                              pathname === item.href ? "text-[#FD904B]" : "text-white"
                            }`}
                            onClick={() => setServicesDropdownOpen(!servicesDropdownOpen)}
                          >
                            {item.name}
                            <motion.div
                              animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronDown className="w-4 h-4" />
                            </motion.div>
                          </button>
                          <AnimatePresence>
                            {servicesDropdownOpen && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="overflow-hidden"
                              >
                                <div className="ml-4 mt-2 space-y-1">
                                  {erpModules.map((module) => (
                                    <Link
                                      key={module.name}
                                      href={module.href}
                                      className="flex items-center gap-2 rounded-lg px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-[#FD904B] transition-colors"
                                      onClick={() => {
                                        setMobileMenuOpen(false)
                                        setServicesDropdownOpen(false)
                                      }}
                                    >
                                      <div className="text-[#FD904B]">{module.icon}</div>
                                      {module.name}
                                    </Link>
                                  ))}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                            pathname === item.href ? "text-[#FD904B]" : "text-white"
                          }`}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </header>
  )
}
