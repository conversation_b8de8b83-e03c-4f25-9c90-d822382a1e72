"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, ChevronDown, ArrowRight } from "lucide-react"


const erpModules = [
  { name: "Dashboard", href: "/services#dashboard" },
  { name: "Annual Calendar", href: "/services#calendar" },
  { name: "Academic Setup", href: "/services#academic" },
  { name: "Transport Fee", href: "/services#transport" },
  { name: "Users/Staff", href: "/services#staff" },
  { name: "Leave Management", href: "/services#leave" },
  { name: "Timetable Overview", href: "/services#timetable" },
  { name: "Proxy Request", href: "/services#proxy" },
  { name: "Enquiry Management", href: "/services#enquiry" },
  { name: "Student Management", href: "/services#student" },
  { name: "Documents", href: "/services#documents" },
  { name: "Student Attendance", href: "/services#attendance" },
  { name: "Sal<PERSON>", href: "/services#salary" },
  { name: "Fee Collection", href: "/services#fees" },
  { name: "Expense Management", href: "/services#expense" },
  { name: "Passbook", href: "/services#passbook" },
  { name: "Circulars", href: "/services#circulars" },
  { name: "Exam", href: "/services#exam" },
  { name: "Gate Pass", href: "/services#gatepass" },
  { name: "Classwork", href: "/services#classwork" },
  { name: "Homework", href: "/services#homework" },
  { name: "Student Leave", href: "/services#studentleave" },
  { name: "Face Attendance", href: "/services#faceattendance" },
]

const navigation = [
  { name: "Home", href: "/" },
  { name: "Service", href: "/services", hasDropdown: true },
  { name: "Support", href: "/support" },
  { name: "About US", href: "/about" },
  { name: "Talk to Us", href: "/contact" },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)
  const pathname = usePathname()

  // Add scroll animation when dropdown opens
  const scrollVariants = {
    hidden: {
      opacity: 0,
      height: 0,
      transition: { duration: 0.3 }
    },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.4,
        ease: "easeOut",
        staggerChildren: 0.02
      }
    }
  }

  return (
    <header className="bg-black text-white sticky top-0 z-50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5">
            <motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-2xl font-bold text-white">
              Rann Dass
            </motion.span>
          </Link>
        </div>

        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        <div className="hidden lg:flex lg:gap-x-12">
          {navigation.map((item) => (
            <div key={item.name} className="relative">
              {item.hasDropdown ? (
                <div
                  className="relative"
                  onMouseEnter={() => setServicesDropdownOpen(true)}
                  onMouseLeave={() => setServicesDropdownOpen(false)}
                >
                  <Link
                    href={item.href}
                    className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] flex items-center gap-1 ${
                      pathname === item.href ? "text-[#FD904B]" : "text-white"
                    }`}
                  >
                    {item.name}
                    <motion.div
                      animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.div>
                  </Link>

                  <AnimatePresence>
                    {servicesDropdownOpen && (
                      <>
                        {/* Backdrop */}
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="fixed inset-0 bg-black/20 z-40"
                          onClick={() => setServicesDropdownOpen(false)}
                        />

                        {/* Dropdown */}
                        <motion.div
                          initial={{ opacity: 0, y: -20, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -20, scale: 0.95 }}
                          transition={{ duration: 0.3, ease: "easeOut" }}
                          className="fixed top-20 left-1/2 transform -translate-x-1/2 w-[90vw] max-w-4xl bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden z-50"
                        >
                        <div className="p-4">
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="text-center mb-4"
                          >
                            <h3 className="text-lg font-bold text-gray-900 mb-1">
                              Comprehensive ERP Modules
                            </h3>
                            <p className="text-gray-600 text-sm">
                              Complete solutions for educational institution management
                            </p>
                          </motion.div>

                          {/* Scrollable container with custom scrollbar and animations */}
                          <motion.div
                            className="max-h-80 overflow-y-auto scrollbar-thin"
                            variants={scrollVariants}
                            initial="hidden"
                            animate="visible"
                            style={{
                              scrollbarWidth: 'thin',
                              scrollbarColor: '#fd904b #f3f4f6'
                            }}
                          >
                            <motion.div
                              className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-3 pr-2"
                              variants={{
                                visible: {
                                  transition: {
                                    staggerChildren: 0.02,
                                    delayChildren: 0.1
                                  }
                                }
                              }}
                            >
                              {erpModules.map((module, index) => (
                                <motion.div
                                  key={module.name}
                                  variants={{
                                    hidden: {
                                      opacity: 0,
                                      y: 20,
                                      scale: 0.8,
                                      rotateX: -15
                                    },
                                    visible: {
                                      opacity: 1,
                                      y: 0,
                                      scale: 1,
                                      rotateX: 0,
                                      transition: {
                                        type: "spring",
                                        stiffness: 100,
                                        damping: 15
                                      }
                                    }
                                  }}
                                  whileHover={{
                                    y: -4,
                                    scale: 1.05,
                                    rotateY: 5,
                                    transition: {
                                      duration: 0.2,
                                      type: "spring",
                                      stiffness: 300
                                    }
                                  }}
                                  whileTap={{ scale: 0.95 }}
                                  className="group"
                                >
                                  <Link
                                    href={module.href}
                                    className="block p-3 rounded-lg hover:bg-gradient-to-br hover:from-gray-50 hover:to-[#FD904B]/5 text-center transition-all duration-300 border border-transparent hover:border-[#FD904B]/20 hover:shadow-md"
                                    onClick={() => setServicesDropdownOpen(false)}
                                  >
                                    <motion.h4
                                      className="text-xs font-medium text-gray-900 group-hover:text-[#FD904B] transition-colors duration-300 leading-tight relative"
                                      whileHover={{ scale: 1.05 }}
                                    >
                                      {module.name}
                                      <motion.div
                                        className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-[#FD904B] to-[#e8813f] origin-left"
                                        initial={{ scaleX: 0, opacity: 0 }}
                                        whileHover={{
                                          scaleX: 1,
                                          opacity: 1,
                                          transition: { duration: 0.3, ease: "easeInOut" }
                                        }}
                                      />
                                    </motion.h4>
                                  </Link>
                                </motion.div>
                              ))}
                            </motion.div>
                          </motion.div>

                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.4 }}
                            className="text-center mt-4 pt-3 border-t border-gray-200"
                          >
                            <Link
                              href="/services"
                              className="inline-flex items-center text-[#FD904B] hover:text-[#e8813f] font-semibold text-sm transition-colors duration-200"
                              onClick={() => setServicesDropdownOpen(false)}
                            >
                              View All Services
                              <ArrowRight className="ml-2 w-4 h-4" />
                            </Link>
                          </motion.div>
                        </div>
                      </motion.div>
                      </>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] ${
                    pathname === item.href ? "text-[#FD904B]" : "text-white"
                  }`}
                >
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </div>
      </nav>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="lg:hidden">
          <div className="fixed inset-0 z-50" />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <Link href="/" className="-m-1.5 p-1.5">
                <span className="text-xl font-bold text-white">Rann Dass</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-white"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <div key={item.name}>
                      {item.hasDropdown ? (
                        <div>
                          <button
                            className={`-mx-3 flex w-full items-center justify-between rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                              pathname === item.href ? "text-[#FD904B]" : "text-white"
                            }`}
                            onClick={() => setServicesDropdownOpen(!servicesDropdownOpen)}
                          >
                            {item.name}
                            <motion.div
                              animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronDown className="w-4 h-4" />
                            </motion.div>
                          </button>
                          <AnimatePresence>
                            {servicesDropdownOpen && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.4, ease: "easeInOut" }}
                                className="overflow-hidden"
                              >
                                <div className="ml-4 mt-4 p-4 bg-gray-900 rounded-xl">
                                  <h4 className="text-[#FD904B] font-semibold mb-4 text-center text-sm">
                                    ERP Modules
                                  </h4>
                                  <div className="grid grid-cols-2 gap-2 max-h-80 overflow-y-auto">
                                    {erpModules.map((module, index) => (
                                      <motion.div
                                        key={module.name}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: index * 0.02 }}
                                      >
                                        <Link
                                          href={module.href}
                                          className="block px-3 py-2 text-gray-300 hover:text-[#FD904B] transition-all duration-300 text-center group"
                                          onClick={() => {
                                            setMobileMenuOpen(false)
                                            setServicesDropdownOpen(false)
                                          }}
                                        >
                                          <span className="text-xs leading-tight relative">
                                            {module.name}
                                            <motion.div
                                              className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#FD904B] origin-left"
                                              initial={{ scaleX: 0 }}
                                              whileHover={{ scaleX: 1 }}
                                              transition={{ duration: 0.3 }}
                                            />
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                            pathname === item.href ? "text-[#FD904B]" : "text-white"
                          }`}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </header>
  )
}
