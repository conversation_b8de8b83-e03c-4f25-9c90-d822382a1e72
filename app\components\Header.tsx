"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, ChevronDown, ArrowRight } from "lucide-react"
import {
  Calendar,
  Settings,
  Bus,
  UserCheck,
  BookOpen,
  HelpCircle,
  GraduationCap,
  FileText,
  UserX,
  DollarSign,
  CreditCard,
  TrendingUp,
  Wallet,
  Bell,
  KeyRound,
  HomeIcon,
  UserMinus,
  Camera,
  ClipboardCheck,
  PenTool,
  Users,
} from "lucide-react"

const erpModules = [
  { name: "Dashboard", icon: <Settings className="w-4 h-4" />, href: "/services#dashboard" },
  { name: "Annual Calendar", icon: <Calendar className="w-4 h-4" />, href: "/services#calendar" },
  { name: "Academic Setup", icon: <BookOpen className="w-4 h-4" />, href: "/services#academic" },
  { name: "Transport Fee", icon: <Bus className="w-4 h-4" />, href: "/services#transport" },
  { name: "Users/Staff", icon: <Users className="w-4 h-4" />, href: "/services#staff" },
  { name: "Leave Management", icon: <UserX className="w-4 h-4" />, href: "/services#leave" },
  { name: "Timetable Overview", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#timetable" },
  { name: "Proxy Request", icon: <UserCheck className="w-4 h-4" />, href: "/services#proxy" },
  { name: "Enquiry Management", icon: <HelpCircle className="w-4 h-4" />, href: "/services#enquiry" },
  { name: "Student Management", icon: <GraduationCap className="w-4 h-4" />, href: "/services#student" },
  { name: "Documents", icon: <FileText className="w-4 h-4" />, href: "/services#documents" },
  { name: "Student Attendance", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#attendance" },
  { name: "Salary", icon: <DollarSign className="w-4 h-4" />, href: "/services#salary" },
  { name: "Fee Collection", icon: <CreditCard className="w-4 h-4" />, href: "/services#fees" },
  { name: "Expense Management", icon: <TrendingUp className="w-4 h-4" />, href: "/services#expense" },
  { name: "Passbook", icon: <Wallet className="w-4 h-4" />, href: "/services#passbook" },
  { name: "Circulars", icon: <Bell className="w-4 h-4" />, href: "/services#circulars" },
  { name: "Exam", icon: <ClipboardCheck className="w-4 h-4" />, href: "/services#exam" },
  { name: "Gate Pass", icon: <KeyRound className="w-4 h-4" />, href: "/services#gatepass" },
  { name: "Classwork", icon: <PenTool className="w-4 h-4" />, href: "/services#classwork" },
  { name: "Homework", icon: <HomeIcon className="w-4 h-4" />, href: "/services#homework" },
  { name: "Student Leave", icon: <UserMinus className="w-4 h-4" />, href: "/services#studentleave" },
  { name: "Face Attendance", icon: <Camera className="w-4 h-4" />, href: "/services#faceattendance" },
]

const navigation = [
  { name: "Home", href: "/" },
  { name: "Service", href: "/services", hasDropdown: true },
  { name: "Support", href: "/support" },
  { name: "About US", href: "/about" },
  { name: "Talk to Us", href: "/contact" },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)
  const pathname = usePathname()

  return (
    <header className="bg-black text-white sticky top-0 z-50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5">
            <motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-2xl font-bold text-white">
              Rann Dass
            </motion.span>
          </Link>
        </div>

        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        <div className="hidden lg:flex lg:gap-x-12">
          {navigation.map((item) => (
            <div key={item.name} className="relative">
              {item.hasDropdown ? (
                <div
                  className="relative"
                  onMouseEnter={() => setServicesDropdownOpen(true)}
                  onMouseLeave={() => setServicesDropdownOpen(false)}
                >
                  <Link
                    href={item.href}
                    className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] flex items-center gap-1 ${
                      pathname === item.href ? "text-[#FD904B]" : "text-white"
                    }`}
                  >
                    {item.name}
                    <motion.div
                      animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.div>
                  </Link>

                  <AnimatePresence>
                    {servicesDropdownOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -20, scale: 0.95 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-screen max-w-6xl bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden z-50"
                        style={{ left: '50%', transform: 'translateX(-50%)' }}
                      >
                        <div className="p-8">
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="text-center mb-8"
                          >
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">
                              Comprehensive ERP Modules
                            </h3>
                            <p className="text-gray-600">
                              Complete solutions for educational institution management
                            </p>
                          </motion.div>

                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                            {erpModules.map((module, index) => (
                              <motion.div
                                key={module.name}
                                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                transition={{
                                  delay: index * 0.03,
                                  duration: 0.4,
                                  ease: "easeOut"
                                }}
                                whileHover={{ y: -5, scale: 1.02 }}
                                className="group"
                              >
                                <Link
                                  href={module.href}
                                  className="block p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 border border-transparent hover:border-[#FD904B]/20 hover:shadow-lg"
                                  onClick={() => setServicesDropdownOpen(false)}
                                >
                                  <motion.div
                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                    transition={{ type: "spring", stiffness: 300 }}
                                    className="flex items-center justify-center w-12 h-12 bg-[#FD904B]/10 rounded-xl mb-3 mx-auto group-hover:bg-[#FD904B]/20 transition-colors duration-300"
                                  >
                                    <div className="text-[#FD904B] group-hover:scale-110 transition-transform">
                                      {module.icon}
                                    </div>
                                  </motion.div>
                                  <h4 className="text-sm font-semibold text-gray-900 text-center group-hover:text-[#FD904B] transition-colors duration-300 leading-tight">
                                    {module.name}
                                  </h4>
                                </Link>
                              </motion.div>
                            ))}
                          </div>

                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                            className="text-center mt-8 pt-6 border-t border-gray-200"
                          >
                            <Link
                              href="/services"
                              className="inline-flex items-center text-[#FD904B] hover:text-[#e8813f] font-semibold transition-colors duration-200"
                              onClick={() => setServicesDropdownOpen(false)}
                            >
                              View All Services
                              <ArrowRight className="ml-2 w-4 h-4" />
                            </Link>
                          </motion.div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`text-sm font-semibold leading-6 transition-colors hover:text-[#FD904B] ${
                    pathname === item.href ? "text-[#FD904B]" : "text-white"
                  }`}
                >
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </div>
      </nav>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="lg:hidden">
          <div className="fixed inset-0 z-50" />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <Link href="/" className="-m-1.5 p-1.5">
                <span className="text-xl font-bold text-white">Rann Dass</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-white"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <div key={item.name}>
                      {item.hasDropdown ? (
                        <div>
                          <button
                            className={`-mx-3 flex w-full items-center justify-between rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                              pathname === item.href ? "text-[#FD904B]" : "text-white"
                            }`}
                            onClick={() => setServicesDropdownOpen(!servicesDropdownOpen)}
                          >
                            {item.name}
                            <motion.div
                              animate={{ rotate: servicesDropdownOpen ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronDown className="w-4 h-4" />
                            </motion.div>
                          </button>
                          <AnimatePresence>
                            {servicesDropdownOpen && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.4, ease: "easeInOut" }}
                                className="overflow-hidden"
                              >
                                <div className="ml-4 mt-4 p-4 bg-gray-900 rounded-xl">
                                  <h4 className="text-[#FD904B] font-semibold mb-4 text-center">
                                    ERP Modules
                                  </h4>
                                  <div className="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto">
                                    {erpModules.map((module, index) => (
                                      <motion.div
                                        key={module.name}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: index * 0.02 }}
                                      >
                                        <Link
                                          href={module.href}
                                          className="flex flex-col items-center gap-2 rounded-lg px-3 py-3 text-gray-300 hover:bg-gray-800 hover:text-[#FD904B] transition-all duration-300 group"
                                          onClick={() => {
                                            setMobileMenuOpen(false)
                                            setServicesDropdownOpen(false)
                                          }}
                                        >
                                          <div className="text-[#FD904B] group-hover:scale-110 transition-transform">
                                            {module.icon}
                                          </div>
                                          <span className="text-xs text-center leading-tight">
                                            {module.name}
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors hover:bg-gray-800 ${
                            pathname === item.href ? "text-[#FD904B]" : "text-white"
                          }`}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </header>
  )
}
