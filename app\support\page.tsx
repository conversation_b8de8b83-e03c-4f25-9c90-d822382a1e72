"use client"

import { motion } from "framer-motion"
import { Phone, Mail, MessageCircle, FileText, CheckCircle } from "lucide-react"

const supportOptions = [
  {
    icon: <Phone className="w-8 h-8" />,
    title: "Phone Support",
    description: "Speak directly with our support team for immediate assistance.",
    availability: "24/7 Available",
    contact: "+****************",
  },
  {
    icon: <Mail className="w-8 h-8" />,
    title: "Email Support",
    description: "Send us your questions and we'll respond within 24 hours.",
    availability: "Response within 24hrs",
    contact: "<EMAIL>",
  },
  {
    icon: <MessageCircle className="w-8 h-8" />,
    title: "Live Chat",
    description: "Chat with our support team in real-time for quick solutions.",
    availability: "Mon-Fri 9AM-6PM",
    contact: "Available on website",
  },
  {
    icon: <FileText className="w-8 h-8" />,
    title: "Documentation",
    description: "Access comprehensive guides and tutorials for self-help.",
    availability: "Always Available",
    contact: "Knowledge Base",
  },
]

const faqItems = [
  {
    question: "What are your support hours?",
    answer:
      "We provide 24/7 phone support for critical issues. Email and chat support are available Monday through Friday, 9 AM to 6 PM EST.",
  },
  {
    question: "How quickly do you respond to support requests?",
    answer:
      "Phone support is immediate, live chat responses are typically within minutes, and email support responses are provided within 24 hours.",
  },
  {
    question: "Do you offer on-site support?",
    answer:
      "Yes, we provide on-site support for enterprise clients and complex technical issues that require physical presence.",
  },
  {
    question: "Is there a cost for support services?",
    answer:
      "Basic support is included with all our service packages. Premium support options are available for clients requiring enhanced service levels.",
  },
  {
    question: "Can you help with training and onboarding?",
    answer:
      "We provide comprehensive training sessions and onboarding assistance to ensure your team can effectively use our solutions.",
  },
  {
    question: "What if I need support outside business hours?",
    answer:
      "Our 24/7 phone support is available for urgent issues. For non-urgent matters, you can submit a ticket through email or our portal.",
  },
]

export default function Support() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mx-auto max-w-3xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
              We're Here to <span className="text-[#FD904B]">Help</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our dedicated support team is committed to providing you with the assistance you need, when you need it.
              Choose from multiple support channels to get help quickly.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Support Options */}
      <section className="py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Multiple Ways to Get Support
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">Choose the support method that works best for you.</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {supportOptions.map((option, index) => (
              <motion.div
                key={option.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow text-center"
              >
                <div className="text-[#FD904B] mb-4 flex justify-center">{option.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{option.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{option.description}</p>
                <div className="text-xs text-gray-500 mb-2">{option.availability}</div>
                <div className="text-sm font-medium text-[#FD904B]">{option.contact}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Stats */}
      <section className="bg-black py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            {[
              { number: "99.9%", label: "Uptime Guarantee" },
              { number: "<2min", label: "Average Response Time" },
              { number: "24/7", label: "Phone Support" },
              { number: "98%", label: "Customer Satisfaction" },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="text-3xl font-bold text-[#FD904B] mb-2">{stat.number}</div>
                <div className="text-white text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="mx-auto max-w-4xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Frequently Asked Questions</h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              Find answers to common questions about our support services.
            </p>
          </motion.div>

          <div className="space-y-6">
            {faqItems.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <CheckCircle className="w-5 h-5 text-[#FD904B] mr-3" />
                  {item.question}
                </h3>
                <p className="text-gray-600 ml-8">{item.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Support */}
      <section className="py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="bg-red-50 border border-red-200 rounded-lg p-8 text-center"
          >
            <h2 className="text-2xl font-bold text-red-800 mb-4">Emergency Support</h2>
            <p className="text-red-700 mb-6">
              For critical issues that require immediate attention, contact our emergency support line.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+15551234567"
                className="bg-red-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-red-700 transition-colors inline-flex items-center justify-center"
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Emergency Line
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-red-600 text-red-600 px-6 py-3 rounded-md font-semibold hover:bg-red-50 transition-colors inline-flex items-center justify-center"
              >
                <Mail className="w-4 h-4 mr-2" />
                Emergency Email
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
