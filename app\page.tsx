"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { ArrowRight, CheckCircle, Users, Award, Clock, Shield } from "lucide-react"
import {
  ChevronDown,
  Calendar,
  Settings,
  Bus,
  UserCheck,
  BookOpen,
  HelpCircle,
  GraduationCap,
  FileText,
  UserX,
  DollarSign,
  CreditCard,
  TrendingUp,
  Wallet,
  Bell,
  KeyRound,
  HomeIcon,
  UserMinus,
  Camera,
  ClipboardCheck,
  PenTool,
} from "lucide-react"

const modules = [
  {
    id: 1,
    title: "Dashboard",
    icon: <Settings className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralized interface for real-time institutional insights",
    details: [
      "Provides a centralized interface for real-time institutional insights",
      "Displays key metrics (e.g., attendance, fees, expenses) in customizable widgets",
      "Offers role-based views for admins, teachers, and parents",
      "Supports quick navigation to all ERP modules (e.g., Timetable, Salary)",
      "Visualizes data with charts for informed decision-making",
      "Enhances user experience with intuitive, interactive design",
    ],
  },
  {
    id: 2,
    title: "Annual Calendar",
    icon: <Calendar className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralizes academic year planning for streamlined operations",
    details: [
      "Centralizes academic year planning for streamlined operations",
      "Schedules events, holidays, and key academic dates with precision",
      "Allows customization to align with institutional curricula",
      "Provides calendar sharing with staff, students, and parents",
      "Generates printable academic calendars for easy reference",
      "Tracks recurring events to ensure consistent scheduling",
    ],
  },
  {
    id: 3,
    title: "Academic Setup",
    icon: <BookOpen className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Configures core academic structure for seamless operations",
    details: [
      "Year: Defines academic years with start/end dates",
      "Department: Manages departments (e.g., Science, Arts) and their hierarchies",
      "Roles: Assigns roles (e.g., teacher, coordinator) with specific permissions",
      "Classroom: Sets up classrooms",
      "Subjects: Defines subjects with that name",
      "Resources: Allocates teaching aids (e.g., projectors, labs)",
      "Timeslots: Schedules class periods to avoid conflicts",
      "Events: Plans academic events like seminars or workshops",
      "Holidays: Marks non-working days for scheduling accuracy",
      "Create Timetable: Generates optimized, conflict-free schedules",
    ],
  },
  {
    id: 4,
    title: "Transport Fee",
    icon: <Bus className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Streamlines transportation logistics for students and staff",
    details: [
      "Vehicles: Tracks vehicle details (e.g., capacity, vehicle details)",
      "Waypoints: Defines pickup/drop-off points with precise locations",
      "Routes: Plans efficient routes to minimize travel time",
      "Assigns students to specific routes for organized transport",
    ],
  },
  {
    id: 5,
    title: "Users/Staff",
    icon: <Users className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages staff profiles with detailed records",
    details: [
      "Manages staff profiles with detailed records (e.g., qualifications, contact)",
      "Assigns roles and permissions for secure system access",
      "Tracks attendance, performance, and professional development activities",
      "Simplifies HR tasks like onboarding and document management",
      "Provides staff dashboards for easy show details and schedules and tasks",
      "Integrates with Salary and Leave Management modules",
    ],
  },
  {
    id: 6,
    title: "Leave Management",
    icon: <UserX className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Automates staff leave processes for efficient HR management",
    details: [
      "Leave Type: Defines categories (e.g., sick leave, casual leave)",
      "Leave Balance: Tracks available leave days per staff member",
      "Second Leave Balance: Manages leave approval by higher authority",
      "Handles leave requests, approvals, and rejections digitally",
      "Notifies supervisors and updates staff records automatically",
      "Generates leave track details for higher authority in education",
    ],
  },
  {
    id: 7,
    title: "Timetable Overview",
    icon: <Clock className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Displays class and staff schedules in user-friendly interface",
    details: [
      "Displays class and staff schedules in a user-friendly interface",
      "Allows real-time adjustments for last-minute changes (e.g., cancellations)",
      "Ensures conflict-free scheduling for classrooms and teachers",
      "Provides printable timetables for students, teachers, and parents",
      "Integrates with Academic Setup for accurate timeslot allocation",
      "Highlights free periods for efficient resource planning",
    ],
  },
  {
    id: 8,
    title: "Proxy Request",
    icon: <UserCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages substitute teacher assignments for absent staff",
    details: [
      "Manages substitute teacher assignments for absent staff",
      "Tracks proxy requests, approvals",
      "Ensures uninterrupted classes by assigning qualified substitutes",
      "Maintains records for accountability and audit purposes",
    ],
  },
  {
    id: 9,
    title: "Enquiry Management",
    icon: <HelpCircle className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Streamlines student admission inquiries from initial contact to enrollment",
    details: [
      "Streamlines student admission inquiries from initial contact to enrollment",
      "Tracks enquiry status, follow-ups, and conversion rates",
      "Stores enquiry details (e.g., parent info, student preferences)",
      "Integrates with Fees Collections for enquiry fee tracking",
      "Improves efficiency in managing large enquiry volumes",
    ],
  },
  {
    id: 10,
    title: "Student Management",
    icon: <GraduationCap className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Maintains comprehensive student profiles",
    details: [
      "Maintains comprehensive student profiles (e.g., personal, academic details)",
      "Tracks enrollment, progress, and disciplinary records",
      "Simplifies student lifecycle from admission to graduation",
      "Provides student dashboards for accessing schedules and grades",
      "Supports bulk updates for class or program changes",
      "Integrates with Attendance and Fees modules for accuracy",
    ],
  },
  {
    id: 11,
    title: "Documents",
    icon: <FileText className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralizes storage for student and staff documents",
    details: [
      "Centralizes storage for student and staff documents (e.g., certificates, IDs)",
      "Ensures secure access with role-based permissions",
      "Supports multiple file formats (e.g., PDF, Word, images)",
      "Allows document categorization and search functionality",
      "Enhances compliance with secure, auditable records",
    ],
  },
  {
    id: 12,
    title: "Student Attendance",
    icon: <ClipboardCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Records daily student attendance with manual entry options",
    details: [
      "Records daily student attendance with manual entry options",
      "Generates detailed attendance reports for classes or individuals",
      "Supports bulk attendance updates for efficiency",
      "Notifies parents of absenteeism via automated alerts",
      "Integrates with Face Attendance for hybrid tracking",
    ],
  },
  {
    id: 13,
    title: "Salary",
    icon: <DollarSign className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages staff payroll with automated calculations",
    details: [
      "Salary Increment: Tracks and applies salary hikes",
      "Salary Slip Generate: Creates monthly pay slips digitally",
      "Salary Details: Maintains detailed payroll records",
      "Supports deductions (e.g., taxes, discount)",
      "Generates payroll reports for financial audits",
      "Ensures timely and accurate salary disbursements",
    ],
  },
  {
    id: 14,
    title: "Fee Collection",
    icon: <CreditCard className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks all fee-related transactions efficiently",
    details: [
      "Fees Payments: Manages student tuition and other fees",
      "Enquiry Payments: Handles fees for admission inquiries",
      "Supports multiple payment methods (e.g., online, cash)",
      "Generates payment receipts and outstanding fee reports",
      "Notifies parents of due dates and payment statuses",
      "Integrates with Passbook for financial transparency",
    ],
  },
  {
    id: 15,
    title: "Expense Management",
    icon: <TrendingUp className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Monitors institutional expenses with detailed tracking",
    details: [
      "Expense Type: Categorizes expenses (e.g., utilities, supplies)",
      "Suppliers: Manages vendor details and payment terms",
      "Expense: Records expenditure with dates and amounts",
      "Supports budget planning and expense approvals",
      "Generates financial reports for audits and reviews",
      "Ensures cost control with transparent tracking",
    ],
  },
  {
    id: 16,
    title: "Passbook",
    icon: <Wallet className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks all financial transactions",
    details: [
      "Tracks all financial transactions (e.g., fees, expenses, salaries)",
      "Maintains detailed institutional account records",
      "Provides real-time balance and transaction history",
      "Supports exportable reports for financial audits",
      "Ensures transparency with secure access controls",
      "Integrates with Fees Collections and Expense Management",
    ],
  },
  {
    id: 17,
    title: "Circulars",
    icon: <Bell className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages institutional announcements and notices",
    details: [
      "Manages institutional announcements and notices",
      "Distributes circulars to staff, students, and parents",
      "Supports templates for quick notice creation",
      "Tracks circular read status for communication tracking",
      "Archives past circulars for future reference",
      "Enhances internal and external communication efficiency",
    ],
  },
  {
    id: 18,
    title: "Exam",
    icon: <ClipboardCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Organizes exam schedules, venues, and required resources efficiently",
    details: [
      "Organizes exam schedules, venues, and required resources efficiently",
      "Tracks student performance with automated grading and result compilation",
      "Sends notifications to students/parents about exam dates and results",
      "Generates detailed performance reports for teachers and administrators",
      "Integrates with Academic Setup for subject-specific exams",
    ],
  },
  {
    id: 19,
    title: "Gate Pass",
    icon: <KeyRound className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Controls campus access for students, staff, and visitors",
    details: [
      "Controls campus access for students, staff, and visitors",
      "Issues digital or printable gate passes with approval workflows",
      "Tracks entry/exit times for security monitoring",
      "Supports visitor registration with ID verification",
      "Integrates with QR code scanning for seamless access control",
      "Enhances campus safety with auditable records",
    ],
  },
  {
    id: 20,
    title: "Classwork",
    icon: <PenTool className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks daily classroom activities, including lessons and tasks",
    details: [
      "Tracks daily classroom activities, including lessons and tasks",
      "Assigns and monitors classwork with deadlines and instructions",
      "Enhances productivity by organizing teacher-student interactions",
      "Allows teachers to upload notes or resources for students",
      "Provides progress tracking for ongoing class assignments",
      "Integrates with Homework for cohesive task management",
    ],
  },
  {
    id: 21,
    title: "Homework",
    icon: <HomeIcon className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Assigns and tracks homework with clear instructions and deadlines",
    details: [
      "Assigns and tracks homework with clear instructions and deadlines",
      "Notifies students and parents via email or app alerts",
      "Allows teachers to review and grade submissions online",
      "Supports file uploads (e.g., PDFs, images) for homework submission",
      "Generates reports on homework completion rates",
      "Enhances parent-teacher communication for student progress",
    ],
  },
  {
    id: 22,
    title: "Student Leave",
    icon: <UserMinus className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages student leave requests with an online approval system",
    details: [
      "Manages student leave requests with an online approval system in mobile",
      "Tracks leave reasons, durations, and approval statuses",
      "Notifies teachers of approved/rejected leaves",
      "Generates leave details for attendance monitoring",
      "Integrates with Student Attendance for accurate records",
      "Supports automated reminders for leave documentation",
    ],
  },
  {
    id: 23,
    title: "Face Attendance System",
    icon: <Camera className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Utilizes advanced biometric facial recognition for automated attendance tracking",
    details: [
      "Utilizes advanced biometric facial recognition for automated attendance tracking",
      "Captures and matches student faces against stored images in real-time",
      "Records student Name, department, classroom, time in, and time out accurately",
      "Reduces manual errors with secure, verified identification processes",
      "Integrates with Student Attendance for hybrid manual records",
      "Generates auditable reports for attendance monitoring and compliance",
    ],
  },
]

const features = [
  {
    icon: <Users className="w-8 h-8" />,
    title: "Expert Team",
    description: "Our experienced professionals deliver exceptional results for your business needs.",
  },
  {
    icon: <Award className="w-8 h-8" />,
    title: "Quality Service",
    description: "We maintain the highest standards of quality in all our service offerings.",
  },
  {
    icon: <Clock className="w-8 h-8" />,
    title: "Timely Delivery",
    description: "We respect your time and ensure all projects are completed on schedule.",
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: "Reliable Support",
    description: "24/7 support to ensure your business operations run smoothly.",
  },
]

const stats = [
  { number: "500+", label: "Happy Clients" },
  { number: "1000+", label: "Projects Completed" },
  { number: "50+", label: "Team Members" },
  { number: "5+", label: "Years Experience" },
]

export default function EduERPPro() {
  const [activeModule, setActiveModule] = useState<number | null>(null)

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 to-white py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl"
            >
              Transform Your School with
              <span className="text-[#FD904B] block">Smart ERP Solution</span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600"
            >
              Streamline your educational institution with our comprehensive School Management ERP system. From
              attendance to fee collection, manage everything seamlessly.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-10 flex items-center justify-center gap-x-6"
            >
              <Link
                href="/services"
                className="rounded-md bg-[#FD904B] px-6 py-3 text-sm font-semibold text-white shadow-sm hover:bg-[#e8813f] transition-colors flex items-center"
              >
                Our Services
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
              <Link
                href="/contact"
                className="text-sm font-semibold leading-6 text-gray-900 hover:text-[#FD904B] transition-colors"
              >
                Talk to Us <span aria-hidden="true">→</span>
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-black py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl lg:text-4xl font-bold text-[#FD904B] mb-2">{stat.number}</div>
                <div className="text-white text-sm lg:text-base">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Why Choose EduERP Pro?</h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              We combine expertise, innovation, and dedication to deliver exceptional results for your educational
              institution.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="text-[#FD904B] mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Modules Section */}
      <section id="modules" className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Comprehensive ERP Modules</h2>
            <p className="text-xl text-gray-600">
              23 powerful modules to manage every aspect of your educational institution
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={module.image || "/placeholder.svg"}
                    alt={module.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-4 left-4 flex items-center">
                    <div className="bg-[#FD904B] p-2 rounded-lg mr-3">{module.icon}</div>
                    <h3 className="text-xl font-semibold text-white">{module.title}</h3>
                  </div>
                </div>
                <div
                  className="p-6 cursor-pointer"
                  onClick={() => setActiveModule(activeModule === module.id ? null : module.id)}
                >
                  <div className="flex items-center justify-between mb-4">
                    <p className="text-gray-600 flex-1">{module.description}</p>
                    <motion.div
                      animate={{ rotate: activeModule === module.id ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown className="w-5 h-5 text-gray-500 ml-4" />
                    </motion.div>
                  </div>
                </div>

                <AnimatePresence>
                  {activeModule === module.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-gray-200 bg-gray-50"
                    >
                      <div className="p-6">
                        <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                        <ul className="space-y-2">
                          {module.details.map((detail, detailIndex) => (
                            <motion.li
                              key={detailIndex}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: detailIndex * 0.1 }}
                              className="flex items-start text-sm text-gray-600"
                            >
                              <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                              {detail}
                            </motion.li>
                          ))}
                        </ul>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose Our ERP?</h2>
            <p className="text-xl text-gray-600">Trusted by schools across the region for excellence and reliability</p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              "User-friendly interface accessible to everyone",
              "24/7 dedicated customer support",
              "Comprehensive suite of modules tailored for schools",
              "Fully compatible with all devices – phones, laptops, and tablets",
              "Quick and easy setup – start using within days",
              "Streamlines daily school operations, saving time and effort",
              "Ensures complete data safety and security",
              "Cost-effective solution, affordable for schools of all sizes",
              "Trusted and used by numerous schools across the region",
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white p-6 rounded-xl shadow-lg"
              >
                <div className="flex items-start">
                  <Shield className="w-6 h-6 text-[#FD904B] mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">{benefit}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-black">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-4">Ready to Transform Your School?</h2>
            <p className="text-xl text-gray-300 mb-8">Get started with our comprehensive School Management ERP today</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-[#FD904B] text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#e8813f] transition-colors"
              >
                Start Free Trial
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors"
              >
                Schedule Demo
              </motion.button>
            </div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-white"
            >
              <ArrowRight className="w-8 h-8 mx-auto mb-4 text-[#FD904B]" />
              <h3 className="text-xl font-semibold mb-2">Call Us</h3>
              <p className="text-gray-300">+1 (555) 123-4567</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-white"
            >
              <ArrowRight className="w-8 h-8 mx-auto mb-4 text-[#FD904B]" />
              <h3 className="text-xl font-semibold mb-2">Email Us</h3>
              <p className="text-gray-300"><EMAIL></p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="text-white"
            >
              <ArrowRight className="w-8 h-8 mx-auto mb-4 text-[#FD904B]" />
              <h3 className="text-xl font-semibold mb-2">Visit Us</h3>
              <p className="text-gray-300">123 Education St, Learning City</p>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
