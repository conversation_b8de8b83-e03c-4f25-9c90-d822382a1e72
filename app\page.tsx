"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { ArrowRight, CheckCircle, Users, Award, Clock, Shield } from "lucide-react"
import {
  ChevronDown,
  Calendar,
  Settings,
  Bus,
  UserCheck,
  BookOpen,
  HelpCircle,
  GraduationCap,
  FileText,
  UserX,
  DollarSign,
  CreditCard,
  TrendingUp,
  Wallet,
  Bell,
  KeyRound,
  HomeIcon,
  UserMinus,
  Camera,
  ClipboardCheck,
  PenTool,
} from "lucide-react"

const modules = [
  {
    id: 1,
    title: "Dashboard",
    icon: <Settings className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralized interface for real-time institutional insights",
    details: [
      "Provides a centralized interface for real-time institutional insights",
      "Displays key metrics (e.g., attendance, fees, expenses) in customizable widgets",
      "Offers role-based views for admins, teachers, and parents",
      "Supports quick navigation to all ERP modules (e.g., Timetable, Salary)",
      "Visualizes data with charts for informed decision-making",
      "Enhances user experience with intuitive, interactive design",
    ],
  },
  {
    id: 2,
    title: "Annual Calendar",
    icon: <Calendar className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralizes academic year planning for streamlined operations",
    details: [
      "Centralizes academic year planning for streamlined operations",
      "Schedules events, holidays, and key academic dates with precision",
      "Allows customization to align with institutional curricula",
      "Provides calendar sharing with staff, students, and parents",
      "Generates printable academic calendars for easy reference",
      "Tracks recurring events to ensure consistent scheduling",
    ],
  },
  {
    id: 3,
    title: "Academic Setup",
    icon: <BookOpen className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Configures core academic structure for seamless operations",
    details: [
      "Year: Defines academic years with start/end dates",
      "Department: Manages departments (e.g., Science, Arts) and their hierarchies",
      "Roles: Assigns roles (e.g., teacher, coordinator) with specific permissions",
      "Classroom: Sets up classrooms",
      "Subjects: Defines subjects with that name",
      "Resources: Allocates teaching aids (e.g., projectors, labs)",
      "Timeslots: Schedules class periods to avoid conflicts",
      "Events: Plans academic events like seminars or workshops",
      "Holidays: Marks non-working days for scheduling accuracy",
      "Create Timetable: Generates optimized, conflict-free schedules",
    ],
  },
  {
    id: 4,
    title: "Transport Fee",
    icon: <Bus className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Streamlines transportation logistics for students and staff",
    details: [
      "Vehicles: Tracks vehicle details (e.g., capacity, vehicle details)",
      "Waypoints: Defines pickup/drop-off points with precise locations",
      "Routes: Plans efficient routes to minimize travel time",
      "Assigns students to specific routes for organized transport",
    ],
  },
  {
    id: 5,
    title: "Users/Staff",
    icon: <Users className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages staff profiles with detailed records",
    details: [
      "Manages staff profiles with detailed records (e.g., qualifications, contact)",
      "Assigns roles and permissions for secure system access",
      "Tracks attendance, performance, and professional development activities",
      "Simplifies HR tasks like onboarding and document management",
      "Provides staff dashboards for easy show details and schedules and tasks",
      "Integrates with Salary and Leave Management modules",
    ],
  },
  {
    id: 6,
    title: "Leave Management",
    icon: <UserX className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Automates staff leave processes for efficient HR management",
    details: [
      "Leave Type: Defines categories (e.g., sick leave, casual leave)",
      "Leave Balance: Tracks available leave days per staff member",
      "Second Leave Balance: Manages leave approval by higher authority",
      "Handles leave requests, approvals, and rejections digitally",
      "Notifies supervisors and updates staff records automatically",
      "Generates leave track details for higher authority in education",
    ],
  },
  {
    id: 7,
    title: "Timetable Overview",
    icon: <Clock className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Displays class and staff schedules in user-friendly interface",
    details: [
      "Displays class and staff schedules in a user-friendly interface",
      "Allows real-time adjustments for last-minute changes (e.g., cancellations)",
      "Ensures conflict-free scheduling for classrooms and teachers",
      "Provides printable timetables for students, teachers, and parents",
      "Integrates with Academic Setup for accurate timeslot allocation",
      "Highlights free periods for efficient resource planning",
    ],
  },
  {
    id: 8,
    title: "Proxy Request",
    icon: <UserCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages substitute teacher assignments for absent staff",
    details: [
      "Manages substitute teacher assignments for absent staff",
      "Tracks proxy requests, approvals",
      "Ensures uninterrupted classes by assigning qualified substitutes",
      "Maintains records for accountability and audit purposes",
    ],
  },
  {
    id: 9,
    title: "Enquiry Management",
    icon: <HelpCircle className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Streamlines student admission inquiries from initial contact to enrollment",
    details: [
      "Streamlines student admission inquiries from initial contact to enrollment",
      "Tracks enquiry status, follow-ups, and conversion rates",
      "Stores enquiry details (e.g., parent info, student preferences)",
      "Integrates with Fees Collections for enquiry fee tracking",
      "Improves efficiency in managing large enquiry volumes",
    ],
  },
  {
    id: 10,
    title: "Student Management",
    icon: <GraduationCap className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Maintains comprehensive student profiles",
    details: [
      "Maintains comprehensive student profiles (e.g., personal, academic details)",
      "Tracks enrollment, progress, and disciplinary records",
      "Simplifies student lifecycle from admission to graduation",
      "Provides student dashboards for accessing schedules and grades",
      "Supports bulk updates for class or program changes",
      "Integrates with Attendance and Fees modules for accuracy",
    ],
  },
  {
    id: 11,
    title: "Documents",
    icon: <FileText className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Centralizes storage for student and staff documents",
    details: [
      "Centralizes storage for student and staff documents (e.g., certificates, IDs)",
      "Ensures secure access with role-based permissions",
      "Supports multiple file formats (e.g., PDF, Word, images)",
      "Allows document categorization and search functionality",
      "Enhances compliance with secure, auditable records",
    ],
  },
  {
    id: 12,
    title: "Student Attendance",
    icon: <ClipboardCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Records daily student attendance with manual entry options",
    details: [
      "Records daily student attendance with manual entry options",
      "Generates detailed attendance reports for classes or individuals",
      "Supports bulk attendance updates for efficiency",
      "Notifies parents of absenteeism via automated alerts",
      "Integrates with Face Attendance for hybrid tracking",
    ],
  },
  {
    id: 13,
    title: "Salary",
    icon: <DollarSign className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages staff payroll with automated calculations",
    details: [
      "Salary Increment: Tracks and applies salary hikes",
      "Salary Slip Generate: Creates monthly pay slips digitally",
      "Salary Details: Maintains detailed payroll records",
      "Supports deductions (e.g., taxes, discount)",
      "Generates payroll reports for financial audits",
      "Ensures timely and accurate salary disbursements",
    ],
  },
  {
    id: 14,
    title: "Fee Collection",
    icon: <CreditCard className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks all fee-related transactions efficiently",
    details: [
      "Fees Payments: Manages student tuition and other fees",
      "Enquiry Payments: Handles fees for admission inquiries",
      "Supports multiple payment methods (e.g., online, cash)",
      "Generates payment receipts and outstanding fee reports",
      "Notifies parents of due dates and payment statuses",
      "Integrates with Passbook for financial transparency",
    ],
  },
  {
    id: 15,
    title: "Expense Management",
    icon: <TrendingUp className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Monitors institutional expenses with detailed tracking",
    details: [
      "Expense Type: Categorizes expenses (e.g., utilities, supplies)",
      "Suppliers: Manages vendor details and payment terms",
      "Expense: Records expenditure with dates and amounts",
      "Supports budget planning and expense approvals",
      "Generates financial reports for audits and reviews",
      "Ensures cost control with transparent tracking",
    ],
  },
  {
    id: 16,
    title: "Passbook",
    icon: <Wallet className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks all financial transactions",
    details: [
      "Tracks all financial transactions (e.g., fees, expenses, salaries)",
      "Maintains detailed institutional account records",
      "Provides real-time balance and transaction history",
      "Supports exportable reports for financial audits",
      "Ensures transparency with secure access controls",
      "Integrates with Fees Collections and Expense Management",
    ],
  },
  {
    id: 17,
    title: "Circulars",
    icon: <Bell className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages institutional announcements and notices",
    details: [
      "Manages institutional announcements and notices",
      "Distributes circulars to staff, students, and parents",
      "Supports templates for quick notice creation",
      "Tracks circular read status for communication tracking",
      "Archives past circulars for future reference",
      "Enhances internal and external communication efficiency",
    ],
  },
  {
    id: 18,
    title: "Exam",
    icon: <ClipboardCheck className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Organizes exam schedules, venues, and required resources efficiently",
    details: [
      "Organizes exam schedules, venues, and required resources efficiently",
      "Tracks student performance with automated grading and result compilation",
      "Sends notifications to students/parents about exam dates and results",
      "Generates detailed performance reports for teachers and administrators",
      "Integrates with Academic Setup for subject-specific exams",
    ],
  },
  {
    id: 19,
    title: "Gate Pass",
    icon: <KeyRound className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Controls campus access for students, staff, and visitors",
    details: [
      "Controls campus access for students, staff, and visitors",
      "Issues digital or printable gate passes with approval workflows",
      "Tracks entry/exit times for security monitoring",
      "Supports visitor registration with ID verification",
      "Integrates with QR code scanning for seamless access control",
      "Enhances campus safety with auditable records",
    ],
  },
  {
    id: 20,
    title: "Classwork",
    icon: <PenTool className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Tracks daily classroom activities, including lessons and tasks",
    details: [
      "Tracks daily classroom activities, including lessons and tasks",
      "Assigns and monitors classwork with deadlines and instructions",
      "Enhances productivity by organizing teacher-student interactions",
      "Allows teachers to upload notes or resources for students",
      "Provides progress tracking for ongoing class assignments",
      "Integrates with Homework for cohesive task management",
    ],
  },
  {
    id: 21,
    title: "Homework",
    icon: <HomeIcon className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Assigns and tracks homework with clear instructions and deadlines",
    details: [
      "Assigns and tracks homework with clear instructions and deadlines",
      "Notifies students and parents via email or app alerts",
      "Allows teachers to review and grade submissions online",
      "Supports file uploads (e.g., PDFs, images) for homework submission",
      "Generates reports on homework completion rates",
      "Enhances parent-teacher communication for student progress",
    ],
  },
  {
    id: 22,
    title: "Student Leave",
    icon: <UserMinus className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Manages student leave requests with an online approval system",
    details: [
      "Manages student leave requests with an online approval system in mobile",
      "Tracks leave reasons, durations, and approval statuses",
      "Notifies teachers of approved/rejected leaves",
      "Generates leave details for attendance monitoring",
      "Integrates with Student Attendance for accurate records",
      "Supports automated reminders for leave documentation",
    ],
  },
  {
    id: 23,
    title: "Face Attendance System",
    icon: <Camera className="w-6 h-6" />,
    image: "/placeholder.svg?height=200&width=300",
    description: "Utilizes advanced biometric facial recognition for automated attendance tracking",
    details: [
      "Utilizes advanced biometric facial recognition for automated attendance tracking",
      "Captures and matches student faces against stored images in real-time",
      "Records student Name, department, classroom, time in, and time out accurately",
      "Reduces manual errors with secure, verified identification processes",
      "Integrates with Student Attendance for hybrid manual records",
      "Generates auditable reports for attendance monitoring and compliance",
    ],
  },
]

const features = [
  {
    icon: <Users className="w-8 h-8" />,
    title: "Expert Team",
    description: "Our experienced professionals deliver exceptional results for your business needs.",
  },
  {
    icon: <Award className="w-8 h-8" />,
    title: "Quality Service",
    description: "We maintain the highest standards of quality in all our service offerings.",
  },
  {
    icon: <Clock className="w-8 h-8" />,
    title: "Timely Delivery",
    description: "We respect your time and ensure all projects are completed on schedule.",
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: "Reliable Support",
    description: "24/7 support to ensure your business operations run smoothly.",
  },
]

const stats = [
  { number: "500+", label: "Happy Clients" },
  { number: "1000+", label: "Projects Completed" },
  { number: "50+", label: "Team Members" },
  { number: "5+", label: "Years Experience" },
]

export default function EduERPPro() {
  const [activeModule, setActiveModule] = useState<number | null>(null)
  const [showAllModules, setShowAllModules] = useState(false)

  const displayedModules = showAllModules ? modules : modules.slice(0, 6)

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 via-white to-gray-100 py-20 lg:py-32 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-32 w-80 h-80 bg-[#FD904B]/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-[#FD904B]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-center lg:text-left">
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <span className="inline-flex items-center rounded-full bg-[#FD904B]/10 px-4 py-2 text-sm font-medium text-[#FD904B] ring-1 ring-inset ring-[#FD904B]/20">
                  🚀 Next-Generation School Management
                </span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl"
              >
                Transform Your School with
                <motion.span
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  className="text-transparent bg-clip-text bg-gradient-to-r from-[#FD904B] to-[#e8813f] block mt-2"
                >
                  Smart ERP Solution
                </motion.span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mt-6 text-lg leading-7 text-gray-600"
              >
                Streamline your educational institution with our comprehensive School Management ERP system.
                From attendance to fee collection, manage everything seamlessly with 23 powerful modules.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mt-8 flex flex-col sm:flex-row items-center lg:items-start lg:justify-start justify-center gap-4"
              >
                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href="/services"
                    className="group relative rounded-xl bg-[#FD904B] px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-[#e8813f] transition-all duration-300 flex items-center overflow-hidden"
                  >
                    <span className="relative z-10">Explore Our Services</span>
                    <motion.div
                      animate={{ x: [0, 4, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    >
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </motion.div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#e8813f] to-[#FD904B] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href="/contact"
                    className="group rounded-xl border-2 border-gray-300 px-6 py-3 text-base font-semibold text-gray-900 hover:border-[#FD904B] hover:text-[#FD904B] transition-all duration-300 flex items-center"
                  >
                    Talk to Us
                    <motion.span
                      className="ml-2"
                      animate={{ x: [0, 3, 0] }}
                      transition={{ repeat: Infinity, duration: 2 }}
                    >
                      →
                    </motion.span>
                  </Link>
                </motion.div>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 1 }}
                className="mt-12 grid grid-cols-3 gap-6 text-center lg:text-left"
              >
                {["500+ Schools", "23 Modules", "24/7 Support"].map((item, index) => (
                  <motion.div
                    key={item}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                    className="group cursor-pointer"
                  >
                    <div className="text-xl font-bold text-[#FD904B] group-hover:scale-110 transition-transform duration-300">
                      {item.split(" ")[0]}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">{item.split(" ").slice(1).join(" ")}</div>
                  </motion.div>
                ))}
              </motion.div>
            </div>

            {/* Right Image */}
            <motion.div
              initial={{ opacity: 0, x: 50, scale: 0.8 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="relative"
            >
              <div className="relative">
                {/* Placeholder for image */}
                <motion.div
                  whileHover={{ scale: 1.02, rotate: 1 }}
                  transition={{ duration: 0.3 }}
                  className="relative bg-gradient-to-br from-[#FD904B]/10 to-[#FD904B]/5 rounded-2xl p-8 shadow-2xl border border-[#FD904B]/20"
                >
                  <div className="aspect-square bg-white rounded-xl shadow-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-6xl mb-4">🏫</div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">School ERP Dashboard</h3>
                      <p className="text-gray-600 text-sm">Modern interface for complete school management</p>
                    </div>
                  </div>
                </motion.div>

                {/* Floating elements */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ repeat: Infinity, duration: 3 }}
                  className="absolute -top-4 -right-4 bg-[#FD904B] text-white p-3 rounded-full shadow-lg"
                >
                  📊
                </motion.div>

                <motion.div
                  animate={{ y: [0, 10, 0] }}
                  transition={{ repeat: Infinity, duration: 2.5, delay: 0.5 }}
                  className="absolute -bottom-4 -left-4 bg-white text-[#FD904B] p-3 rounded-full shadow-lg border border-[#FD904B]/20"
                >
                  📚
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative bg-black py-20 overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #FD904B 2px, transparent 2px)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4">Trusted by Educational Institutions</h2>
            <p className="text-gray-400 text-lg">Join thousands of schools already using our platform</p>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15, duration: 0.6 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="text-center group"
              >
                <div className="relative">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.15 + 0.3, type: "spring", stiffness: 200 }}
                    className="text-4xl lg:text-6xl font-bold text-[#FD904B] mb-3 group-hover:text-white transition-colors duration-300"
                  >
                    {stat.number}
                  </motion.div>
                  <div className="absolute inset-0 bg-[#FD904B]/20 rounded-full blur-xl scale-0 group-hover:scale-100 transition-transform duration-500"></div>
                </div>
                <div className="text-white text-sm lg:text-lg font-medium group-hover:text-[#FD904B] transition-colors duration-300">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Decorative elements */}
          <div className="absolute top-10 left-10 w-20 h-20 border border-[#FD904B]/30 rounded-full"></div>
          <div className="absolute bottom-10 right-10 w-16 h-16 bg-[#FD904B]/20 rounded-full blur-sm"></div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-20 w-32 h-32 bg-[#FD904B]/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-[#FD904B]/5 rounded-full blur-2xl"></div>
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mx-auto max-w-3xl text-center mb-20"
          >
            <motion.span
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className="inline-block text-[#FD904B] font-semibold text-lg mb-4"
            >
              ✨ Why Choose Us
            </motion.span>
            <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
              Why Choose EduERP Pro?
            </h2>
            <p className="text-xl leading-8 text-gray-600">
              We combine expertise, innovation, and dedication to deliver exceptional results for your educational
              institution.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.15, duration: 0.6 }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group relative"
              >
                <div className="relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden">
                  {/* Gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* Icon container */}
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    className="relative inline-flex items-center justify-center w-16 h-16 bg-[#FD904B]/10 rounded-2xl mb-6 group-hover:bg-[#FD904B]/20 transition-colors duration-300"
                  >
                    <div className="text-[#FD904B] group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                  </motion.div>

                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#FD904B] transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                    {feature.description}
                  </p>

                  {/* Decorative corner */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#FD904B]/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Modules Section */}
      <section id="modules" className="py-24 bg-white relative">
        <div className="container mx-auto px-4 max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.span
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className="inline-block text-[#FD904B] font-semibold text-lg mb-4"
            >
              🎯 Our Solutions
            </motion.span>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Comprehensive ERP Modules
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              23 powerful modules designed to manage every aspect of your educational institution with precision and ease
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {displayedModules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.05, duration: 0.6 }}
                whileHover={{ y: -5 }}
                className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-gray-100"
              >
                <div className="relative h-56 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/20 to-[#FD904B]/5"></div>
                  <img
                    src={module.image || "/placeholder.svg"}
                    alt={module.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

                  {/* Module header */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center mb-2">
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        className="bg-[#FD904B] p-3 rounded-xl mr-3 shadow-lg"
                      >
                        <div className="text-white">{module.icon}</div>
                      </motion.div>
                      <h3 className="text-xl font-bold text-white group-hover:text-[#FD904B] transition-colors duration-300">
                        {module.title}
                      </h3>
                    </div>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-[#FD904B]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>

                <div
                  className="p-6 cursor-pointer"
                  onClick={() => setActiveModule(activeModule === module.id ? null : module.id)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <p className="text-gray-600 flex-1 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                      {module.description}
                    </p>
                    <motion.div
                      animate={{ rotate: activeModule === module.id ? 180 : 0 }}
                      transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                      className="ml-4 flex-shrink-0"
                    >
                      <div className="w-8 h-8 rounded-full bg-gray-100 group-hover:bg-[#FD904B]/10 flex items-center justify-center transition-colors duration-300">
                        <ChevronDown className="w-4 h-4 text-gray-500 group-hover:text-[#FD904B] transition-colors duration-300" />
                      </div>
                    </motion.div>
                  </div>
                </div>

                <AnimatePresence>
                  {activeModule === module.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.4, ease: "easeInOut" }}
                      className="border-t border-gray-200 bg-gradient-to-br from-gray-50 to-white overflow-hidden"
                    >
                      <div className="p-6">
                        <motion.div
                          initial={{ y: 20, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ delay: 0.1 }}
                        >
                          <h4 className="font-bold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-[#FD904B] rounded-full mr-3"></div>
                            Key Features:
                          </h4>
                        </motion.div>
                        <ul className="space-y-3">
                          {module.details.map((detail, detailIndex) => (
                            <motion.li
                              key={detailIndex}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: detailIndex * 0.05 + 0.2, duration: 0.4 }}
                              className="flex items-start text-sm text-gray-700 group/item"
                            >
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: detailIndex * 0.05 + 0.3, type: "spring", stiffness: 200 }}
                                className="mr-3 mt-0.5 flex-shrink-0"
                              >
                                <CheckCircle className="w-4 h-4 text-[#FD904B] group-hover/item:scale-110 transition-transform duration-200" />
                              </motion.div>
                              <span className="group-hover/item:text-gray-900 transition-colors duration-200">
                                {detail}
                              </span>
                            </motion.li>
                          ))}
                        </ul>

                        {/* Call to action */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5 }}
                          className="mt-6 pt-4 border-t border-gray-200"
                        >
                          <Link
                            href="/contact"
                            className="inline-flex items-center text-sm font-semibold text-[#FD904B] hover:text-[#e8813f] transition-colors duration-200"
                          >
                            Learn more about {module.title}
                            <ArrowRight className="ml-2 w-4 h-4" />
                          </Link>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {/* View More Button */}
          {!showAllModules && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="text-center mt-16"
            >
              <motion.button
                onClick={() => setShowAllModules(true)}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group relative inline-flex items-center justify-center bg-[#FD904B] text-white px-10 py-4 rounded-2xl text-lg font-semibold hover:bg-[#e8813f] transition-all duration-300 shadow-lg hover:shadow-2xl overflow-hidden"
              >
                <span className="relative z-10 mr-3">View More Modules</span>
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-r from-[#e8813f] to-[#FD904B] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </motion.button>
              <p className="text-gray-600 mt-4 text-sm">
                Discover {modules.length - 6} more powerful modules
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-24 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-64 h-64 bg-[#FD904B]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-[#FD904B]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container mx-auto px-4 max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.span
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className="inline-block text-[#FD904B] font-semibold text-lg mb-4"
            >
              🏆 Our Advantages
            </motion.span>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Why Choose Our ERP?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Trusted by schools across the region for excellence, reliability, and innovation in educational management
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { text: "User-friendly interface accessible to everyone", icon: "👥" },
              { text: "24/7 dedicated customer support", icon: "🕒" },
              { text: "Comprehensive suite of modules tailored for schools", icon: "📚" },
              { text: "Fully compatible with all devices – phones, laptops, and tablets", icon: "📱" },
              { text: "Quick and easy setup – start using within days", icon: "⚡" },
              { text: "Streamlines daily school operations, saving time and effort", icon: "⏰" },
              { text: "Ensures complete data safety and security", icon: "🔒" },
              { text: "Cost-effective solution, affordable for schools of all sizes", icon: "💰" },
              { text: "Trusted and used by numerous schools across the region", icon: "🌟" },
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.08, duration: 0.6 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group relative"
              >
                <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden">
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative flex items-start">
                    <motion.div
                      whileHover={{ scale: 1.2, rotate: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                      className="text-3xl mr-4 mt-1 flex-shrink-0"
                    >
                      {benefit.icon}
                    </motion.div>
                    <div className="flex-1">
                      <Shield className="w-6 h-6 text-[#FD904B] mb-3 group-hover:scale-110 transition-transform duration-300" />
                      <p className="text-gray-700 leading-relaxed group-hover:text-gray-900 transition-colors duration-300">
                        {benefit.text}
                      </p>
                    </div>
                  </div>

                  {/* Decorative corner */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#FD904B]/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call to action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
            className="text-center mt-16"
          >
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-[#FD904B] text-white font-semibold rounded-2xl hover:bg-[#e8813f] transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Start Your Journey Today
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 bg-black relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-40 h-40 border border-[#FD904B]/20 rounded-full"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-[#FD904B]/10 rounded-full blur-2xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-[#FD904B]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container mx-auto px-4 max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.span
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className="inline-block text-[#FD904B] font-semibold text-lg mb-4"
            >
              🚀 Get Started
            </motion.span>
            <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
              Ready to Transform Your School?
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
              Join hundreds of educational institutions already using our comprehensive School Management ERP system
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href="/contact"
                  className="group relative inline-flex items-center justify-center bg-[#FD904B] text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-[#e8813f] transition-all duration-300 shadow-lg hover:shadow-2xl overflow-hidden"
                >
                  <span className="relative z-10">Start Free Trial</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#e8813f] to-[#FD904B] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href="/contact"
                  className="group inline-flex items-center justify-center border-2 border-white text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300 shadow-lg hover:shadow-2xl"
                >
                  Schedule Demo
                </Link>
              </motion.div>
            </div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              { icon: "📞", title: "Call Us", info: "+1 (555) 123-4567" },
              { icon: "✉️", title: "Email Us", info: "<EMAIL>" },
              { icon: "📍", title: "Visit Us", info: "123 Education St, Learning City" }
            ].map((contact, index) => (
              <motion.div
                key={contact.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group text-center"
              >
                <div className="bg-white/5 backdrop-blur-sm p-8 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-500">
                  <motion.div
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    className="text-4xl mb-4"
                  >
                    {contact.icon}
                  </motion.div>
                  <div className="w-12 h-12 bg-[#FD904B] rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <ArrowRight className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-[#FD904B] transition-colors duration-300">
                    {contact.title}
                  </h3>
                  <p className="text-gray-300 group-hover:text-white transition-colors duration-300">
                    {contact.info}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
