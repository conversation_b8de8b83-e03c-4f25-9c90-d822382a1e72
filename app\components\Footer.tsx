"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }}>
              <h3 className="text-2xl font-bold mb-4">Ra<PERSON></h3>
              <p className="text-gray-300 mb-6 max-w-md">
                Providing professional services and innovative solutions to help your business grow and succeed in
                today's competitive market.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                  <Facebook className="w-5 h-5" />
                </a>
                <a href="#" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                  <Twitter className="w-5 h-5" />
                </a>
                <a href="#" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                  <Linkedin className="w-5 h-5" />
                </a>
                <a href="#" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                  <Instagram className="w-5 h-5" />
                </a>
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
            >
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="/services" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                    Services
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/support" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                    Support
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-300 hover:text-[#FD904B] transition-colors">
                    Contact
                  </Link>
                </li>
              </ul>
            </motion.div>
          </div>

          {/* Contact Info */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-3 text-[#FD904B]" />
                  <span className="text-gray-300">+****************</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-3 text-[#FD904B]" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-3 text-[#FD904B]" />
                  <span className="text-gray-300">123 Business St, City, State</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-800">
          <p className="text-center text-gray-300">© 2024 Rann Dass. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
