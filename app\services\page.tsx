"use client"

import { motion } from "framer-motion"
import { Code, Palette, Search, BarChart, Shield, Headphones, ArrowRight } from "lucide-react"

const services = [
  {
    icon: <Code className="w-8 h-8" />,
    title: "Web Development",
    description: "Custom web applications built with modern technologies and best practices.",
    features: ["Responsive Design", "Modern Frameworks", "Performance Optimization", "SEO Ready"],
  },
  {
    icon: <Palette className="w-8 h-8" />,
    title: "UI/UX Design",
    description: "Beautiful and intuitive user interfaces that enhance user experience.",
    features: ["User Research", "Wireframing", "Prototyping", "Visual Design"],
  },
  {
    icon: <Search className="w-8 h-8" />,
    title: "Digital Marketing",
    description: "Comprehensive digital marketing strategies to grow your online presence.",
    features: ["SEO Optimization", "Social Media", "Content Marketing", "PPC Campaigns"],
  },
  {
    icon: <BarChart className="w-8 h-8" />,
    title: "Business Analytics",
    description: "Data-driven insights to help you make informed business decisions.",
    features: ["Data Analysis", "Reporting", "KPI Tracking", "Business Intelligence"],
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: "Cybersecurity",
    description: "Protect your business with comprehensive security solutions.",
    features: ["Security Audits", "Risk Assessment", "Compliance", "Monitoring"],
  },
  {
    icon: <Headphones className="w-8 h-8" />,
    title: "IT Consulting",
    description: "Expert guidance to optimize your technology infrastructure.",
    features: ["Technology Strategy", "System Integration", "Cloud Migration", "IT Support"],
  },
]

export default function Services() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mx-auto max-w-3xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
              Our <span className="text-[#FD904B]">Services</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              We offer a comprehensive range of professional services designed to help your business succeed in today's
              digital landscape.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow group"
              >
                <div className="text-[#FD904B] mb-6 group-hover:scale-110 transition-transform">{service.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <ArrowRight className="w-4 h-4 text-[#FD904B] mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <button className="w-full bg-gray-900 text-white py-2 px-4 rounded-md hover:bg-[#FD904B] transition-colors">
                  Learn More
                </button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="bg-gray-50 py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Our Process</h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              We follow a proven methodology to ensure successful project delivery.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Discovery", description: "Understanding your needs and requirements" },
              { step: "02", title: "Planning", description: "Creating a detailed project roadmap" },
              { step: "03", title: "Execution", description: "Implementing solutions with precision" },
              { step: "04", title: "Delivery", description: "Launching and providing ongoing support" },
            ].map((item, index) => (
              <motion.div
                key={item.step}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#FD904B] text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-black py-20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">Ready to Start Your Project?</h2>
            <p className="mt-4 text-lg leading-8 text-gray-300">
              Let's discuss how our services can help your business grow and succeed.
            </p>
            <div className="mt-8">
              <a
                href="/contact"
                className="rounded-md bg-[#FD904B] px-6 py-3 text-sm font-semibold text-white shadow-sm hover:bg-[#e8813f] transition-colors inline-flex items-center"
              >
                Get Started Today
                <ArrowRight className="ml-2 w-4 h-4" />
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
